/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：SerializationConfiguration.java
 * 包    名：com.whiskerguard.ai.config
 * 描    述：序列化配置类，解决Kryo循环引用问题
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/30
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * 序列化配置类
 * <p>
 * 主要功能：
 * 1. 配置Jackson ObjectMapper避免循环引用
 * 2. 提供Redis序列化编解码器
 * 3. 处理Hibernate懒加载问题
 * 4. 优化JSON序列化性能
 *
 * <AUTHOR>
 * @since 1.0
 */
@Configuration
public class SerializationConfiguration {

    /**
     * 配置主要的ObjectMapper Bean
     * <p>
     * 用于解决以下问题：
     * 1. Hibernate懒加载序列化问题
     * 2. 循环引用问题
     * 3. 时间格式处理
     * 4. 空值处理
     *
     * @return 配置好的ObjectMapper实例
     */
    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // 注册Java时间模块
        mapper.registerModule(new JavaTimeModule());
        
        // 禁用将日期写为时间戳
        mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        
        // 忽略未知属性，避免版本兼容问题
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        
        // 忽略空的Bean，避免序列化错误
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        
        // 只序列化非空值，减少数据量
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        
        // 处理Hibernate懒加载问题
        mapper.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
        
        return mapper;
    }

    /**
     * 配置Redis专用的JsonJacksonCodec
     * <p>
     * 使用自定义的ObjectMapper来避免Kryo序列化问题
     *
     * @return 配置好的JsonJacksonCodec实例
     */
    @Bean("redisJsonCodec")
    public JsonJacksonCodec redisJsonCodec() {
        ObjectMapper redisMapper = new ObjectMapper();
        
        // 注册Java时间模块
        redisMapper.registerModule(new JavaTimeModule());
        
        // 禁用将日期写为时间戳
        redisMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        
        // 忽略未知属性
        redisMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        
        // 忽略空的Bean
        redisMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        
        // 只序列化非空值
        redisMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        
        // 关键：处理循环引用问题
        redisMapper.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);
        
        // 忽略Hibernate代理对象
        redisMapper.configure(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE, false);
        
        return new JsonJacksonCodec(redisMapper);
    }
}

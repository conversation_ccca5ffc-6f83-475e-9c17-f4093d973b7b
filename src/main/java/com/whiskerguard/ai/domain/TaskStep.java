package com.whiskerguard.ai.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 任务步骤实体
 * 记录Agent任务的执行步骤
 */
@Entity
@Table(name = "task_step")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class TaskStep implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 步骤名称
     */
    @NotNull
    @Size(max = 100)
    @Column(name = "step_name", length = 100, nullable = false)
    private String stepName;

    /**
     * 步骤描述
     */
    @Size(max = 500)
    @Column(name = "step_description", length = 500)
    private String stepDescription;

    /**
     * 步骤类型（检索、生成、验证等）
     * Step type (retrieval, generation, validation, etc.)
     */
    @Size(max = 50)
    @Column(name = "step_type", length = 50)
    private String stepType;

    /**
     * 步骤状态
     */
    @NotNull
    @Column(name = "status", nullable = false)
    private String status;

    /**
     * 步骤顺序
     */
    @NotNull
    @Column(name = "step_order", nullable = false)
    private Integer stepOrder;

    /**
     * 输入数据
     */
    @Lob
    @Column(name = "input_data")
    private String inputData;

    /**
     * 输出数据
     */
    @Lob
    @Column(name = "output_data")
    private String outputData;

    /**
     * 错误信息
     */
    @Size(max = 1000)
    @Column(name = "error_message", length = 1000)
    private String errorMessage;

    /**
     * 开始时间
     */
    @Column(name = "start_time")
    private Instant startTime;

    /**
     * 结束时间
     */
    @Column(name = "end_time")
    private Instant endTime;

    /**
     * 执行时长(毫秒)
     */
    @Column(name = "execution_time")
    private Long executionTime;

    /**
     * 重试次数
     */
    @Min(value = 0)
    @Column(name = "retry_count")
    private Integer retryCount;

    /**
     * 扩展元数据
     */
    @Lob
    @Column(name = "metadata")
    private String metadata;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @ManyToOne(optional = false)
    @NotNull
    @JsonIgnoreProperties(value = { "taskSteps", "contexts" }, allowSetters = true)
    @com.fasterxml.jackson.annotation.JsonBackReference("agentTask-taskSteps")
    private AgentTask agentTask;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public TaskStep id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public TaskStep tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getStepName() {
        return this.stepName;
    }

    public TaskStep stepName(String stepName) {
        this.setStepName(stepName);
        return this;
    }

    public void setStepName(String stepName) {
        this.stepName = stepName;
    }

    public String getStepDescription() {
        return this.stepDescription;
    }

    public TaskStep stepDescription(String stepDescription) {
        this.setStepDescription(stepDescription);
        return this;
    }

    public void setStepDescription(String stepDescription) {
        this.stepDescription = stepDescription;
    }

    public String getStepType() {
        return this.stepType;
    }

    public TaskStep stepType(String stepType) {
        this.setStepType(stepType);
        return this;
    }

    public void setStepType(String stepType) {
        this.stepType = stepType;
    }

    public String getStatus() {
        return this.status;
    }

    public TaskStep status(String status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getStepOrder() {
        return this.stepOrder;
    }

    public TaskStep stepOrder(Integer stepOrder) {
        this.setStepOrder(stepOrder);
        return this;
    }

    public void setStepOrder(Integer stepOrder) {
        this.stepOrder = stepOrder;
    }

    public String getInputData() {
        return this.inputData;
    }

    public TaskStep inputData(String inputData) {
        this.setInputData(inputData);
        return this;
    }

    public void setInputData(String inputData) {
        this.inputData = inputData;
    }

    public String getOutputData() {
        return this.outputData;
    }

    public TaskStep outputData(String outputData) {
        this.setOutputData(outputData);
        return this;
    }

    public void setOutputData(String outputData) {
        this.outputData = outputData;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public TaskStep errorMessage(String errorMessage) {
        this.setErrorMessage(errorMessage);
        return this;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Instant getStartTime() {
        return this.startTime;
    }

    public TaskStep startTime(Instant startTime) {
        this.setStartTime(startTime);
        return this;
    }

    public void setStartTime(Instant startTime) {
        this.startTime = startTime;
    }

    public Instant getEndTime() {
        return this.endTime;
    }

    public TaskStep endTime(Instant endTime) {
        this.setEndTime(endTime);
        return this;
    }

    public void setEndTime(Instant endTime) {
        this.endTime = endTime;
    }

    public Long getExecutionTime() {
        return this.executionTime;
    }

    public TaskStep executionTime(Long executionTime) {
        this.setExecutionTime(executionTime);
        return this;
    }

    public void setExecutionTime(Long executionTime) {
        this.executionTime = executionTime;
    }

    public Integer getRetryCount() {
        return this.retryCount;
    }

    public TaskStep retryCount(Integer retryCount) {
        this.setRetryCount(retryCount);
        return this;
    }

    public void setRetryCount(Integer retryCount) {
        this.retryCount = retryCount;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public TaskStep metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public TaskStep version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public TaskStep createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public TaskStep createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public TaskStep updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public TaskStep updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public TaskStep isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public AgentTask getAgentTask() {
        return this.agentTask;
    }

    public void setAgentTask(AgentTask agentTask) {
        this.agentTask = agentTask;
    }

    public TaskStep agentTask(AgentTask agentTask) {
        this.setAgentTask(agentTask);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TaskStep)) {
            return false;
        }
        return getId() != null && getId().equals(((TaskStep) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "TaskStep{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", stepName='" + getStepName() + "'" +
            ", stepDescription='" + getStepDescription() + "'" +
            ", status='" + getStatus() + "'" +
            ", stepOrder=" + getStepOrder() +
            ", inputData='" + getInputData() + "'" +
            ", outputData='" + getOutputData() + "'" +
            ", errorMessage='" + getErrorMessage() + "'" +
            ", startTime='" + getStartTime() + "'" +
            ", endTime='" + getEndTime() + "'" +
            ", executionTime=" + getExecutionTime() +
            ", retryCount=" + getRetryCount() +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}
